import type { Metadata } from 'next'
import Link from 'next/link'
import Navbar from '@/components/Navbar'
import Footer from '@/components/Footer'

export const metadata: Metadata = {
  title: 'خريطة الموقع - عجائب الخبراء',
  description: 'خريطة موقع عجائب الخبراء - جميع صفحات وأقسام الموقع في مكان واحد',
  robots: 'noindex, nofollow',
}

export default function SitemapPage() {
  const sitePages = [
    {
      title: 'الصفحات الرئيسية',
      pages: [
        { name: 'الرئيسية', href: '/', description: 'الصفحة الرئيسية للموقع' },
        { name: 'المطابخ', href: '/kitchens', description: 'معرض تصاميم المطابخ العصرية والكلاسيكية' },
        { name: 'الخزانات', href: '/cabinets', description: 'معرض تصاميم الخزائن والدواليب' },
      ]
    },
    {
      title: 'معلومات الشركة',
      pages: [
        { name: 'من نحن', href: '#about', description: 'معلومات عن شركة عجائب الخبراء' },
        { name: 'تواصل معنا', href: '#contact', description: 'طرق التواصل مع فريق الشركة' },
      ]
    },
    {
      title: 'الصفحات القانونية',
      pages: [
        { name: 'سياسة الخصوصية', href: '/privacy', description: 'سياسة حماية البيانات والخصوصية' },
        { name: 'الشروط والأحكام', href: '/terms', description: 'شروط وأحكام استخدام الخدمات' },
        { name: 'خريطة الموقع', href: '/sitemap-page', description: 'دليل جميع صفحات الموقع' },
      ]
    },
    {
      title: 'لوحة التحكم',
      pages: [
        { name: 'لوحة التحكم', href: '/admin', description: 'لوحة تحكم إدارة المحتوى (للمديرين فقط)' },
      ]
    }
  ]

  return (
    <div className="bg-gray-50" dir="rtl">
      <Navbar />
      
      <main className="pt-20">
        {/* Header */}
        <section className="bg-gradient-to-br from-primary-600 to-primary-800 py-20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
              خريطة الموقع
            </h1>
            <p className="text-xl text-white/90">
              دليل شامل لجميع صفحات وأقسام موقع عجائب الخبراء
            </p>
          </div>
        </section>

        {/* Content */}
        <section className="py-16">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            
            {/* Introduction */}
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                استكشف جميع أقسام موقعنا
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                تصفح خريطة الموقع للوصول السريع إلى جميع الصفحات والخدمات المتاحة في موقع عجائب الخبراء
              </p>
            </div>

            {/* Site Map Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {sitePages.map((section, sectionIndex) => (
                <div key={sectionIndex} className="bg-white rounded-2xl shadow-lg p-8 hover:shadow-xl transition-shadow duration-300">
                  <div className="flex items-center mb-6">
                    <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center mr-4">
                      <i className="ri-folder-line text-white text-xl"></i>
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900">
                      {section.title}
                    </h3>
                  </div>
                  
                  <div className="space-y-4">
                    {section.pages.map((page, pageIndex) => (
                      <div key={pageIndex} className="border-r-4 border-primary-200 pr-4 hover:border-primary-500 transition-colors duration-300">
                        <Link 
                          href={page.href}
                          className="block group"
                        >
                          <h4 className="text-lg font-semibold text-gray-900 group-hover:text-primary-600 transition-colors duration-300 mb-2">
                            {page.name}
                          </h4>
                          <p className="text-gray-600 text-sm leading-relaxed">
                            {page.description}
                          </p>
                        </Link>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>

            {/* Quick Links */}
            <div className="mt-16 bg-gradient-to-br from-primary-50 to-blue-50 rounded-2xl p-8">
              <h3 className="text-2xl font-bold text-gray-900 text-center mb-8">
                روابط سريعة
              </h3>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                <Link 
                  href="/"
                  className="bg-white rounded-xl p-6 text-center hover:shadow-lg transition-all duration-300 group"
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <i className="ri-home-line text-white text-xl"></i>
                  </div>
                  <h4 className="font-semibold text-gray-900 group-hover:text-primary-600 transition-colors">
                    الرئيسية
                  </h4>
                </Link>

                <Link 
                  href="/kitchens"
                  className="bg-white rounded-xl p-6 text-center hover:shadow-lg transition-all duration-300 group"
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <i className="ri-restaurant-line text-white text-xl"></i>
                  </div>
                  <h4 className="font-semibold text-gray-900 group-hover:text-primary-600 transition-colors">
                    المطابخ
                  </h4>
                </Link>

                <Link 
                  href="/cabinets"
                  className="bg-white rounded-xl p-6 text-center hover:shadow-lg transition-all duration-300 group"
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <i className="ri-archive-line text-white text-xl"></i>
                  </div>
                  <h4 className="font-semibold text-gray-900 group-hover:text-primary-600 transition-colors">
                    الخزانات
                  </h4>
                </Link>

                <Link 
                  href="#contact"
                  className="bg-white rounded-xl p-6 text-center hover:shadow-lg transition-all duration-300 group"
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <i className="ri-phone-line text-white text-xl"></i>
                  </div>
                  <h4 className="font-semibold text-gray-900 group-hover:text-primary-600 transition-colors">
                    تواصل معنا
                  </h4>
                </Link>
              </div>
            </div>

            {/* Contact Section */}
            <div className="mt-16 text-center">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">
                هل تحتاج مساعدة؟
              </h3>
              <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
                إذا لم تجد ما تبحث عنه في خريطة الموقع، لا تتردد في التواصل معنا للحصول على المساعدة
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href="https://wa.me/966500000000"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-green-500 hover:bg-green-600 text-white px-8 py-4 rounded-full font-bold text-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 flex items-center gap-3 justify-center"
                >
                  <i className="ri-whatsapp-line text-xl"></i>
                  تواصل عبر واتساب
                </Link>
                
                <Link
                  href="tel:+966500000000"
                  className="bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 rounded-full font-bold text-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 flex items-center gap-3 justify-center"
                >
                  <i className="ri-phone-line text-xl"></i>
                  اتصل بنا الآن
                </Link>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  )
}
