'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'

interface SocialMedia {
  id: number
  platform: string
  url: string
  icon: string
}

interface QuickLink {
  id: number
  text: string
  href: string
}

interface ContactInfo {
  id: number
  icon: string
  text: string
  type: string
}

interface FooterSettings {
  copyright_text: string
}

interface FooterData {
  socialMedia: SocialMedia[]
  quickLinks: QuickLink[]
  contactInfo: ContactInfo[]
  settings: FooterSettings
}

const Footer = () => {
  const [footerData, setFooterData] = useState<FooterData | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchFooterData = async () => {
      try {
        const response = await fetch('/api/footer')
        if (response.ok) {
          const data = await response.json()
          setFooterData(data)
        }
      } catch (error) {
        console.error('Error fetching footer data:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchFooterData()
  }, [])

  // Default data if no data from database
  const defaultData: FooterData = {
    socialMedia: [
      { id: 1, platform: 'واتساب', url: 'https://wa.me/966500000000', icon: 'ri-whatsapp-line' },
      { id: 2, platform: 'تويتر', url: 'https://twitter.com', icon: 'ri-twitter-x-line' },
      { id: 3, platform: 'إنستغرام', url: 'https://instagram.com', icon: 'ri-instagram-line' },
      { id: 4, platform: 'فيسبوك', url: 'https://facebook.com', icon: 'ri-facebook-line' },
    ],
    quickLinks: [
      { id: 1, text: 'الرئيسية', href: '/' },
      { id: 2, text: 'المطابخ', href: '/kitchens' },
      { id: 3, text: 'الخزانات', href: '/cabinets' },
      { id: 4, text: 'من نحن', href: '#about' },
      { id: 5, text: 'تواصل معنا', href: '#contact' },
    ],
    contactInfo: [
      { id: 1, icon: 'ri-map-pin-line', text: 'الرياض، المملكة العربية السعودية', type: 'address' },
      { id: 2, icon: 'ri-phone-line', text: '+966 50 000 0000', type: 'phone' },
      { id: 3, icon: 'ri-mail-line', text: '<EMAIL>', type: 'email' },
      { id: 4, icon: 'ri-time-line', text: 'السبت - الخميس: 8:00 ص - 6:00 م', type: 'hours' },
    ],
    settings: {
      copyright_text: '© 2024 عجائب الخبراء. جميع الحقوق محفوظة.'
    }
  }

  const data = footerData || defaultData

  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-1">
            <div className="flex items-center space-x-2 space-x-reverse mb-6">
              <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-700 rounded-xl flex items-center justify-center">
                <span className="text-white font-bold text-xl">ع</span>
              </div>
              <div className="flex flex-col">
                <span className="font-bold text-xl text-white">
                  عجائب الخبراء
                </span>
                <span className="text-sm text-gray-400">
                  Expert Wonders
                </span>
              </div>
            </div>
            <p className="text-gray-400 leading-relaxed mb-6">
              شركة رائدة في تصميم وتنفيذ المطابخ والخزائن العصرية والكلاسيكية بأعلى معايير الجودة والحرفية في المملكة العربية السعودية.
            </p>
            
            {/* Social Media */}
            <div className="flex space-x-4 space-x-reverse">
              {data.socialMedia.map((social) => (
                <a
                  key={social.id}
                  href={social.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-gray-800 hover:bg-primary-600 rounded-lg flex items-center justify-center transition-colors duration-300"
                  title={social.platform}
                >
                  <i className={`${social.icon} text-lg`}></i>
                </a>
              ))}
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-bold mb-6">روابط سريعة</h3>
            <ul className="space-y-3">
              {data.quickLinks.map((link) => (
                <li key={link.id}>
                  <Link
                    href={link.href}
                    className="text-gray-400 hover:text-white transition-colors duration-300"
                  >
                    {link.text}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-bold mb-6">معلومات التواصل</h3>
            <ul className="space-y-4">
              {data.contactInfo.map((contact) => (
                <li key={contact.id} className="flex items-start space-x-3 space-x-reverse">
                  <i className={`${contact.icon} text-lg mt-1`}></i>
                  <span className="text-gray-400 leading-relaxed">
                    {contact.text}
                  </span>
                </li>
              ))}
            </ul>
          </div>

          {/* Newsletter */}
          <div>
            <h3 className="text-lg font-bold mb-6">ابق على تواصل</h3>
            <p className="text-gray-400 mb-4">
              اشترك في نشرتنا الإخبارية للحصول على آخر العروض والتحديثات
            </p>
            <div className="flex flex-col space-y-3">
              <input
                type="email"
                placeholder="البريد الإلكتروني"
                className="bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-primary-500 transition-colors"
              />
              <button className="bg-gradient-to-r from-primary-500 to-primary-600 text-white px-6 py-3 rounded-lg font-medium hover:from-primary-600 hover:to-primary-700 transition-all duration-300">
                اشتراك
              </button>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm mb-4 md:mb-0">
              {data.settings.copyright_text}
            </p>
            <div className="flex space-x-6 space-x-reverse text-sm text-gray-400">
              <Link href="/privacy" className="hover:text-white transition-colors">
                سياسة الخصوصية
              </Link>
              <Link href="/terms" className="hover:text-white transition-colors">
                الشروط والأحكام
              </Link>
              <Link href="/sitemap-page" className="hover:text-white transition-colors">
                خريطة الموقع
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
