'use client'

import { motion } from 'framer-motion'

const CallToAction = () => {
  return (
    <section className="py-20 bg-gradient-to-r from-primary-600 via-primary-700 to-primary-800 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-primary-600/90 to-primary-800/90"></div>
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-white/10 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-white/10 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-white/5 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center"
        >
          {/* Main Content */}
          <div className="max-w-4xl mx-auto">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.1 }}
              viewport={{ once: true }}
              className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight"
            >
              جاهز لتحويل مطبخك إلى تحفة فنية؟
            </motion.h2>

            <motion.p
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              className="text-xl md:text-2xl text-white/90 mb-8 leading-relaxed"
            >
              احصل على استشارة مجانية وعرض سعر مخصص لمشروعك اليوم
            </motion.p>

            <motion.p
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              viewport={{ once: true }}
              className="text-lg text-white/80 mb-12 max-w-2xl mx-auto"
            >
              فريقنا من الخبراء جاهز لمساعدتك في تصميم وتنفيذ مطبخ أحلامك بأعلى معايير الجودة والحرفية
            </motion.p>

            {/* Contact Options */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
              className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-12"
            >
              {/* WhatsApp Button */}
              <a
                href="https://wa.me/966557611105"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-green-500 hover:bg-green-600 text-white px-8 py-4 rounded-full font-bold text-lg transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 flex items-center gap-3 min-w-[250px] justify-center"
              >
                <i className="ri-whatsapp-line text-xl"></i>
                تواصل عبر واتساب
              </a>

              {/* Phone Button */}
              <a
                href="tel:+966557611105"
                className="bg-white text-primary-600 px-8 py-4 rounded-full font-bold text-lg hover:bg-gray-100 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 flex items-center gap-3 min-w-[250px] justify-center"
              >
                <i className="ri-phone-line text-xl"></i>
                اتصل بنا الآن
              </a>
            </motion.div>

            {/* Features */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
              viewport={{ once: true }}
              className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto"
            >
              {[
                {
                  icon: 'ri-target-line',
                  title: 'استشارة مجانية',
                  description: 'احصل على استشارة مجانية من خبرائنا'
                },
                {
                  icon: 'ri-ruler-line',
                  title: 'قياس مجاني',
                  description: 'نقوم بزيارة موقعك وأخذ القياسات مجاناً'
                },
                {
                  icon: 'ri-money-dollar-circle-line',
                  title: 'عرض سعر مفصل',
                  description: 'احصل على عرض سعر شامل ومفصل'
                }
              ].map((feature, index) => (
                <div key={index} className="text-center">
                  <div className="mb-4">
                    <i className={`${feature.icon} text-4xl text-white`}></i>
                  </div>
                  <h3 className="text-xl font-bold text-white mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-white/80">
                    {feature.description}
                  </p>
                </div>
              ))}
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default CallToAction
