(()=>{var e={};e.id=977,e.ids=[977],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1581:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c});var s=t(687),a=t(3210),l=t(9190),n=t(1317),i=t(5814),o=t.n(i);function c(){let[e,r]=(0,a.useState)({name:"",phone:"",email:"",subject:"",message:"",projectType:"kitchen"}),[t,i]=(0,a.useState)(!1),[c,d]=(0,a.useState)("idle"),m=e=>{let{name:t,value:s}=e.target;r(e=>({...e,[t]:s}))},x=async e=>{e.preventDefault(),i(!0);try{await new Promise(e=>setTimeout(e,1e3)),d("success"),r({name:"",phone:"",email:"",subject:"",message:"",projectType:"kitchen"})}catch(e){d("error")}finally{i(!1)}};return(0,s.jsxs)("div",{className:"bg-gray-50",dir:"rtl",children:[(0,s.jsx)(l.default,{}),(0,s.jsxs)("main",{className:"pt-20",children:[(0,s.jsx)("section",{className:"bg-gradient-to-br from-primary-600 to-primary-800 py-20",children:(0,s.jsxs)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,s.jsx)("h1",{className:"text-4xl md:text-5xl font-bold text-white mb-6",children:"تواصل معنا"}),(0,s.jsx)("p",{className:"text-xl text-white/90 max-w-3xl mx-auto leading-relaxed",children:"نحن هنا لمساعدتك في تحويل أحلامك إلى واقع. تواصل معنا للحصول على استشارة مجانية"})]})}),(0,s.jsx)("section",{className:"py-16 bg-white",children:(0,s.jsx)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[{icon:"ri-phone-line",title:"اتصل بنا",info:"+966 55 761 1105",action:"tel:+966557611105",color:"from-blue-500 to-blue-600"},{icon:"ri-whatsapp-line",title:"واتساب",info:"+966 55 761 1105",action:"https://wa.me/966557611105",color:"from-green-500 to-green-600"},{icon:"ri-mail-line",title:"البريد الإلكتروني",info:"<EMAIL>",action:"mailto:<EMAIL>",color:"from-purple-500 to-purple-600"},{icon:"ri-map-pin-line",title:"العنوان",info:"الرياض، المملكة العربية السعودية",action:"#map",color:"from-orange-500 to-orange-600"}].map((e,r)=>(0,s.jsxs)(o(),{href:e.action,target:e.action.startsWith("http")?"_blank":void 0,rel:e.action.startsWith("http")?"noopener noreferrer":void 0,className:"bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 text-center group",children:[(0,s.jsx)("div",{className:`w-16 h-16 bg-gradient-to-br ${e.color} rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`,children:(0,s.jsx)("i",{className:`${e.icon} text-white text-2xl`})}),(0,s.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-2",children:e.title}),(0,s.jsx)("p",{className:"text-gray-600 text-sm",children:e.info})]},r))})})}),(0,s.jsx)("section",{className:"py-16 bg-gray-50",children:(0,s.jsx)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,s.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-lg",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"أرسل لنا رسالة"}),"success"===c&&(0,s.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 mb-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("i",{className:"ri-check-circle-line text-green-500 text-xl mr-3"}),(0,s.jsx)("p",{className:"text-green-700",children:"تم إرسال رسالتك بنجاح! سنتواصل معك قريباً."})]})}),"error"===c&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("i",{className:"ri-error-warning-line text-red-500 text-xl mr-3"}),(0,s.jsx)("p",{className:"text-red-700",children:"حدث خطأ في إرسال الرسالة. يرجى المحاولة مرة أخرى."})]})}),(0,s.jsxs)("form",{onSubmit:x,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"الاسم الكامل *"}),(0,s.jsx)("input",{type:"text",id:"name",name:"name",value:e.name,onChange:m,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors",placeholder:"أدخل اسمك الكامل"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-2",children:"رقم الهاتف *"}),(0,s.jsx)("input",{type:"tel",id:"phone",name:"phone",value:e.phone,onChange:m,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors",placeholder:"05xxxxxxxx"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"البريد الإلكتروني"}),(0,s.jsx)("input",{type:"email",id:"email",name:"email",value:e.email,onChange:m,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors",placeholder:"<EMAIL>"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"projectType",className:"block text-sm font-medium text-gray-700 mb-2",children:"نوع المشروع *"}),(0,s.jsxs)("select",{id:"projectType",name:"projectType",value:e.projectType,onChange:m,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors",children:[(0,s.jsx)("option",{value:"kitchen",children:"مطبخ"}),(0,s.jsx)("option",{value:"cabinet",children:"خزانة"}),(0,s.jsx)("option",{value:"both",children:"مطبخ وخزانة"}),(0,s.jsx)("option",{value:"consultation",children:"استشارة"}),(0,s.jsx)("option",{value:"other",children:"أخرى"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"subject",className:"block text-sm font-medium text-gray-700 mb-2",children:"الموضوع"}),(0,s.jsx)("input",{type:"text",id:"subject",name:"subject",value:e.subject,onChange:m,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors",placeholder:"موضوع الرسالة"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-2",children:"الرسالة *"}),(0,s.jsx)("textarea",{id:"message",name:"message",value:e.message,onChange:m,required:!0,rows:5,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors resize-none",placeholder:"اكتب رسالتك هنا..."})]}),(0,s.jsx)("button",{type:"submit",disabled:t,className:"w-full bg-gradient-to-r from-primary-500 to-primary-600 text-white py-4 px-6 rounded-lg font-bold text-lg hover:from-primary-600 hover:to-primary-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center gap-3",children:t?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white"}),"جاري الإرسال..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"ri-send-plane-line text-xl"}),"إرسال الرسالة"]})})]})]}),(0,s.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-lg",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"موقعنا على الخريطة"}),(0,s.jsx)("div",{id:"map",className:"w-full h-96 bg-gray-200 rounded-lg overflow-hidden",children:(0,s.jsx)("iframe",{src:"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3624.2!2d46.6753!3d24.7136!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3e2f03890d489399%3A0xba974d1c98e79fd5!2sRiyadh%20Saudi%20Arabia!5e0!3m2!1sen!2s!4v1640000000000!5m2!1sen!2s",width:"100%",height:"100%",style:{border:0},allowFullScreen:!0,loading:"lazy",referrerPolicy:"no-referrer-when-downgrade",title:"موقع عجائب الخبراء"})}),(0,s.jsxs)("div",{className:"mt-6 space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-start space-x-3 space-x-reverse",children:[(0,s.jsx)("i",{className:"ri-map-pin-line text-primary-500 text-xl mt-1"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold text-gray-900",children:"العنوان"}),(0,s.jsx)("p",{className:"text-gray-600",children:"الرياض، المملكة العربية السعودية"})]})]}),(0,s.jsxs)("div",{className:"flex items-start space-x-3 space-x-reverse",children:[(0,s.jsx)("i",{className:"ri-time-line text-primary-500 text-xl mt-1"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold text-gray-900",children:"ساعات العمل"}),(0,s.jsx)("p",{className:"text-gray-600",children:"السبت - الخميس: 8:00 ص - 6:00 م"}),(0,s.jsx)("p",{className:"text-gray-600",children:"الجمعة: مغلق"})]})]})]})]})]})})}),(0,s.jsx)("section",{className:"py-16 bg-gradient-to-r from-primary-600 to-primary-800",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,s.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-white mb-6",children:"تحتاج مساعدة فورية؟"}),(0,s.jsx)("p",{className:"text-xl text-white/90 mb-8 max-w-2xl mx-auto",children:"تواصل معنا مباشرة للحصول على رد سريع واستشارة فورية"}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-6 justify-center",children:[(0,s.jsxs)(o(),{href:"https://wa.me/966557611105",target:"_blank",rel:"noopener noreferrer",className:"bg-green-500 hover:bg-green-600 text-white px-8 py-4 rounded-full font-bold text-lg transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 flex items-center gap-3 justify-center",children:[(0,s.jsx)("i",{className:"ri-whatsapp-line text-xl"}),"واتساب فوري"]}),(0,s.jsxs)(o(),{href:"tel:+966557611105",className:"bg-white text-primary-600 px-8 py-4 rounded-full font-bold text-lg hover:bg-gray-100 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 flex items-center gap-3 justify-center",children:[(0,s.jsx)("i",{className:"ri-phone-line text-xl"}),"اتصال مباشر"]})]})]})})]}),(0,s.jsx)(n.default,{})]})}},2553:(e,r,t)=>{Promise.resolve().then(t.bind(t,1581))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3839:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/var/www/html/src/app/contact/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/var/www/html/src/app/contact/page.tsx","default")},3873:e=>{"use strict";e.exports=require("path")},6105:(e,r,t)=>{Promise.resolve().then(t.bind(t,3839))},6487:()=>{},8335:()=>{},8448:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>c});var s=t(5239),a=t(8088),l=t(8170),n=t.n(l),i=t(893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);t.d(r,o);let c={children:["",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,3839)),"/var/www/html/src/app/contact/page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,9864)),"/var/www/html/src/app/contact/layout.tsx"],metadata:{icon:[],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"/var/www/html/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]}.children,d=["/var/www/html/src/app/contact/page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/contact/page",pathname:"/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9864:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a,metadata:()=>s});let s={title:"تواصل معنا - عجائب الخبراء",description:"تواصل مع شركة عجائب الخبراء للحصول على استشارة مجانية وعرض سعر مخصص لمشروع مطبخك أو خزانتك. نحن هنا لمساعدتك",keywords:["تواصل معنا","عجائب الخبراء","استشارة مطابخ","عرض سعر مطبخ","خدمة عملاء"],alternates:{canonical:"/contact"},openGraph:{title:"تواصل معنا - عجائب الخبراء",description:"تواصل مع شركة عجائب الخبراء للحصول على استشارة مجانية وعرض سعر مخصص لمشروع مطبخك أو خزانتك",url:"https://khobrakitchens.com/contact",type:"website"}};function a({children:e}){return e}}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,317,866,814,434],()=>t(8448));module.exports=s})();