[{"/var/www/html/src/app/admin/AdminPageContent.tsx": "1", "/var/www/html/src/app/admin/page.tsx": "2", "/var/www/html/src/app/api/cabinets/[id]/route.ts": "3", "/var/www/html/src/app/api/cabinets/route.ts": "4", "/var/www/html/src/app/api/categories/route.ts": "5", "/var/www/html/src/app/api/footer/route.ts": "6", "/var/www/html/src/app/api/hero/route.ts": "7", "/var/www/html/src/app/api/kitchens/[id]/route.ts": "8", "/var/www/html/src/app/api/kitchens/route.ts": "9", "/var/www/html/src/app/api/uploads/route.ts": "10", "/var/www/html/src/app/api/why-choose-us/route.ts": "11", "/var/www/html/src/app/cabinets/CabinetsPageContent.tsx": "12", "/var/www/html/src/app/cabinets/page.tsx": "13", "/var/www/html/src/app/kitchens/KitchensPageContent.tsx": "14", "/var/www/html/src/app/kitchens/page.tsx": "15", "/var/www/html/src/app/layout.tsx": "16", "/var/www/html/src/app/manifest.ts": "17", "/var/www/html/src/app/page.tsx": "18", "/var/www/html/src/app/sitemap.ts": "19", "/var/www/html/src/components/CabinetGallery.tsx": "20", "/var/www/html/src/components/CallToAction.tsx": "21", "/var/www/html/src/components/Footer.tsx": "22", "/var/www/html/src/components/HeroSection.tsx": "23", "/var/www/html/src/components/KitchenGallery.tsx": "24", "/var/www/html/src/components/Navbar.tsx": "25", "/var/www/html/src/components/ProductModal.tsx": "26", "/var/www/html/src/components/Testimonials.tsx": "27", "/var/www/html/src/components/WhyChooseUs.tsx": "28", "/var/www/html/src/components/admin/AdminDashboard.tsx": "29", "/var/www/html/src/components/admin/AdminHeader.tsx": "30", "/var/www/html/src/components/admin/AdminLogin.tsx": "31", "/var/www/html/src/components/admin/AdminSidebar.tsx": "32", "/var/www/html/src/components/admin/sections/CabinetsManagement.tsx": "33", "/var/www/html/src/components/admin/sections/DashboardHome.tsx": "34", "/var/www/html/src/components/admin/sections/FooterManagement.tsx": "35", "/var/www/html/src/components/admin/sections/HeroManagement.tsx": "36", "/var/www/html/src/components/admin/sections/KitchensManagement.tsx": "37", "/var/www/html/src/lib/database/connection.ts": "38", "/var/www/html/src/types/index.ts": "39", "/var/www/html/src/utils/index.ts": "40", "/var/www/html/src/app/privacy/page.tsx": "41", "/var/www/html/src/app/sitemap-page/page.tsx": "42", "/var/www/html/src/app/terms/page.tsx": "43"}, {"size": 2066, "mtime": 1752448273750, "results": "44", "hashOfConfig": "45"}, {"size": 437, "mtime": 1752448254202, "results": "46", "hashOfConfig": "45"}, {"size": 2640, "mtime": 1752449425546, "results": "47", "hashOfConfig": "45"}, {"size": 1842, "mtime": 1752443814089, "results": "48", "hashOfConfig": "45"}, {"size": 1940, "mtime": 1752443960773, "results": "49", "hashOfConfig": "45"}, {"size": 3480, "mtime": 1752443880188, "results": "50", "hashOfConfig": "45"}, {"size": 2470, "mtime": 1752443855723, "results": "51", "hashOfConfig": "45"}, {"size": 2633, "mtime": 1752449299658, "results": "52", "hashOfConfig": "45"}, {"size": 1834, "mtime": 1752443775073, "results": "53", "hashOfConfig": "45"}, {"size": 2747, "mtime": 1752448902278, "results": "54", "hashOfConfig": "45"}, {"size": 3199, "mtime": 1752443940998, "results": "55", "hashOfConfig": "45"}, {"size": 9736, "mtime": 1752448213174, "results": "56", "hashOfConfig": "45"}, {"size": 1141, "mtime": 1752448170828, "results": "57", "hashOfConfig": "45"}, {"size": 9683, "mtime": 1752448098050, "results": "58", "hashOfConfig": "45"}, {"size": 1157, "mtime": 1752448056568, "results": "59", "hashOfConfig": "45"}, {"size": 3038, "mtime": 1752443639402, "results": "60", "hashOfConfig": "45"}, {"size": 1357, "mtime": 1752452387912, "results": "61", "hashOfConfig": "45"}, {"size": 2046, "mtime": 1752443688252, "results": "62", "hashOfConfig": "45"}, {"size": 585, "mtime": 1752448821345, "results": "63", "hashOfConfig": "45"}, {"size": 6769, "mtime": 1752447853012, "results": "64", "hashOfConfig": "45"}, {"size": 5667, "mtime": 1752452005566, "results": "65", "hashOfConfig": "45"}, {"size": 7472, "mtime": 1752452596030, "results": "66", "hashOfConfig": "45"}, {"size": 6379, "mtime": 1752444044747, "results": "67", "hashOfConfig": "45"}, {"size": 6743, "mtime": 1752447819582, "results": "68", "hashOfConfig": "45"}, {"size": 4433, "mtime": 1752451800380, "results": "69", "hashOfConfig": "45"}, {"size": 9511, "mtime": 1752448141532, "results": "70", "hashOfConfig": "45"}, {"size": 6122, "mtime": 1752452052382, "results": "71", "hashOfConfig": "45"}, {"size": 6906, "mtime": 1752452145067, "results": "72", "hashOfConfig": "45"}, {"size": 2227, "mtime": 1752448336829, "results": "73", "hashOfConfig": "45"}, {"size": 4706, "mtime": 1752448393256, "results": "74", "hashOfConfig": "45"}, {"size": 5073, "mtime": 1752448313626, "results": "75", "hashOfConfig": "45"}, {"size": 3669, "mtime": 1752448364330, "results": "76", "hashOfConfig": "45"}, {"size": 7141, "mtime": 1752448617590, "results": "77", "hashOfConfig": "45"}, {"size": 7289, "mtime": 1752448446801, "results": "78", "hashOfConfig": "45"}, {"size": 10529, "mtime": 1752448664217, "results": "79", "hashOfConfig": "45"}, {"size": 10749, "mtime": 1752448499122, "results": "80", "hashOfConfig": "45"}, {"size": 7094, "mtime": 1752448579810, "results": "81", "hashOfConfig": "45"}, {"size": 2645, "mtime": 1752443743647, "results": "82", "hashOfConfig": "45"}, {"size": 2504, "mtime": 1752448704193, "results": "83", "hashOfConfig": "45"}, {"size": 5343, "mtime": 1752448737884, "results": "84", "hashOfConfig": "45"}, {"size": 7932, "mtime": 1752452452356, "results": "85", "hashOfConfig": "45"}, {"size": 9878, "mtime": 1752452556050, "results": "86", "hashOfConfig": "45"}, {"size": 10659, "mtime": 1752452509456, "results": "87", "hashOfConfig": "45"}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "nkml1y", {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/var/www/html/src/app/admin/AdminPageContent.tsx", [], [], "/var/www/html/src/app/admin/page.tsx", [], [], "/var/www/html/src/app/api/cabinets/[id]/route.ts", [], [], "/var/www/html/src/app/api/cabinets/route.ts", [], [], "/var/www/html/src/app/api/categories/route.ts", [], [], "/var/www/html/src/app/api/footer/route.ts", [], [], "/var/www/html/src/app/api/hero/route.ts", [], [], "/var/www/html/src/app/api/kitchens/[id]/route.ts", [], [], "/var/www/html/src/app/api/kitchens/route.ts", [], [], "/var/www/html/src/app/api/uploads/route.ts", [], [], "/var/www/html/src/app/api/why-choose-us/route.ts", [], [], "/var/www/html/src/app/cabinets/CabinetsPageContent.tsx", [], [], "/var/www/html/src/app/cabinets/page.tsx", [], [], "/var/www/html/src/app/kitchens/KitchensPageContent.tsx", [], [], "/var/www/html/src/app/kitchens/page.tsx", [], [], "/var/www/html/src/app/layout.tsx", [], [], "/var/www/html/src/app/manifest.ts", [], [], "/var/www/html/src/app/page.tsx", [], [], "/var/www/html/src/app/sitemap.ts", [], [], "/var/www/html/src/components/CabinetGallery.tsx", [], [], "/var/www/html/src/components/CallToAction.tsx", [], [], "/var/www/html/src/components/Footer.tsx", [], [], "/var/www/html/src/components/HeroSection.tsx", [], [], "/var/www/html/src/components/KitchenGallery.tsx", [], [], "/var/www/html/src/components/Navbar.tsx", [], [], "/var/www/html/src/components/ProductModal.tsx", [], [], "/var/www/html/src/components/Testimonials.tsx", [], [], "/var/www/html/src/components/WhyChooseUs.tsx", [], [], "/var/www/html/src/components/admin/AdminDashboard.tsx", [], [], "/var/www/html/src/components/admin/AdminHeader.tsx", [], [], "/var/www/html/src/components/admin/AdminLogin.tsx", [], [], "/var/www/html/src/components/admin/AdminSidebar.tsx", [], [], "/var/www/html/src/components/admin/sections/CabinetsManagement.tsx", [], [], "/var/www/html/src/components/admin/sections/DashboardHome.tsx", [], [], "/var/www/html/src/components/admin/sections/FooterManagement.tsx", [], [], "/var/www/html/src/components/admin/sections/HeroManagement.tsx", [], [], "/var/www/html/src/components/admin/sections/KitchensManagement.tsx", [], [], "/var/www/html/src/lib/database/connection.ts", [], [], "/var/www/html/src/types/index.ts", [], [], "/var/www/html/src/utils/index.ts", [], [], "/var/www/html/src/app/privacy/page.tsx", [], [], "/var/www/html/src/app/sitemap-page/page.tsx", [], [], "/var/www/html/src/app/terms/page.tsx", [], []]