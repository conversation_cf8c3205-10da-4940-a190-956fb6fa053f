(()=>{var e={};e.id=126,e.ids=[126],e.modules={418:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});let a=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/var/www/html/src/app/cabinets/CabinetsPageContent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/var/www/html/src/app/cabinets/CabinetsPageContent.tsx","default")},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1544:(e,t,s)=>{"use strict";s.d(t,{default:()=>x});var a=s(687),r=s(3210),l=s(6001),i=s(2036),n=s(3372);s(2822),s(5866),s(4120);var o=s(9190),c=s(1317),d=s(6680);let x=()=>{let[e,t]=(0,r.useState)([]),[s,x]=(0,r.useState)([]),[m,h]=(0,r.useState)("all"),[u,p]=(0,r.useState)(null),[g,f]=(0,r.useState)(!0);(0,r.useEffect)(()=>{(async()=>{try{let[e,s]=await Promise.all([fetch("/api/cabinets"),fetch("/api/categories?type=cabinet")]);if(e.ok){let s=await e.json();t(s)}if(s.ok){let e=await s.json();x(e)}}catch(e){console.error("Error fetching data:",e)}finally{f(!1)}})()},[]);let b="all"===m?e:e.filter(e=>e.category_slug===m);return g?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",dir:"rtl",children:[(0,a.jsx)(o.default,{}),(0,a.jsx)("div",{className:"pt-20 flex items-center justify-center min-h-screen",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-secondary-500"})}),(0,a.jsx)(c.default,{})]}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",dir:"rtl",children:[(0,a.jsx)(o.default,{}),(0,a.jsx)("section",{className:"pt-20 pb-16 bg-gradient-to-br from-secondary-600 to-secondary-800",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)(l.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"text-center text-white",children:[(0,a.jsx)("h1",{className:"text-4xl md:text-5xl lg:text-6xl font-bold mb-6",children:"معرض الخزانات"}),(0,a.jsx)("p",{className:"text-xl md:text-2xl mb-8 max-w-3xl mx-auto",children:"تصفح مجموعتنا الرائعة من الخزانات المصممة خصيصاً لتناسب احتياجاتك وتضفي لمسة أناقة على منزلك"}),(0,a.jsxs)("div",{className:"text-lg opacity-90",children:[e.length," خزانة متاحة"]})]})})}),s.length>0&&(0,a.jsx)("section",{className:"py-8 bg-white border-b",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex flex-wrap justify-center gap-4",children:[(0,a.jsx)("button",{onClick:()=>h("all"),className:`px-6 py-3 rounded-full font-medium transition-all duration-300 ${"all"===m?"bg-secondary-500 text-white shadow-lg":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:"جميع الخزانات"}),s.map(e=>(0,a.jsx)("button",{onClick:()=>h(e.slug),className:`px-6 py-3 rounded-full font-medium transition-all duration-300 ${m===e.slug?"bg-secondary-500 text-white shadow-lg":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:e.name},e.id))]})})}),(0,a.jsx)("section",{className:"py-16",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:b.length>0?(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8",children:b.map((e,t)=>(0,a.jsxs)(l.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.1*t},className:"group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden cursor-pointer",onClick:()=>p(e),children:[(0,a.jsxs)("div",{className:"relative h-64 overflow-hidden",children:[e.images&&e.images.length>0?(0,a.jsx)(i.RC,{modules:[n.Vx,n.dK,n.Ij],navigation:!0,pagination:{clickable:!0},autoplay:{delay:3e3,disableOnInteraction:!1},className:"h-full",children:e.images.map(t=>(0,a.jsx)(i.qr,{children:(0,a.jsx)("img",{src:t.image_url,alt:t.alt_text||e.title,className:"w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"})},t.id))}):(0,a.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-gray-500 text-lg",children:"لا توجد صور"})}),e.category_name&&(0,a.jsx)("div",{className:"absolute top-4 right-4 bg-secondary-500 text-white px-3 py-1 rounded-full text-sm font-medium",children:e.category_name}),(0,a.jsx)("div",{className:"absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center",children:(0,a.jsx)("button",{className:"bg-white text-secondary-600 px-6 py-3 rounded-full font-bold hover:bg-gray-100 transition-colors",children:"عرض التفاصيل"})})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3 group-hover:text-secondary-600 transition-colors",children:e.title}),e.description&&(0,a.jsx)("p",{className:"text-gray-600 line-clamp-2 mb-4",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[e.images?.length||0," صورة"]}),(0,a.jsx)("span",{className:"text-secondary-500 font-medium",children:"عرض المزيد ←"})]})]})]},e.id))}):(0,a.jsxs)("div",{className:"text-center py-16",children:[(0,a.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDD0D"}),(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"لا توجد خزانات في هذه الفئة"}),(0,a.jsx)("p",{className:"text-gray-600 mb-8",children:"جرب تصفح فئة أخرى أو عرض جميع الخزانات"}),(0,a.jsx)("button",{onClick:()=>h("all"),className:"bg-secondary-500 text-white px-8 py-3 rounded-full font-bold hover:bg-secondary-600 transition-colors",children:"عرض جميع الخزانات"})]})})}),u&&(0,a.jsx)(d.A,{product:u,onClose:()=>p(null),type:"cabinet"}),(0,a.jsx)(c.default,{})]})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},5218:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i,metadata:()=>l});var a=s(7413),r=s(418);let l={title:"الخزانات - عجائب الخبراء",description:"تصفح مجموعتنا الرائعة من الخزانات المصممة خصيصاً لتناسب احتياجاتك وتضفي لمسة أناقة على منزلك في المملكة العربية السعودية.",keywords:["خزانات السعودية","خزانات ملابس","خزانات عصرية","خزانات كلاسيكية","تصميم خزانات","عجائب الخبراء"],alternates:{canonical:"/cabinets"},openGraph:{title:"الخزانات - عجائب الخبراء",description:"تصفح مجموعتنا الرائعة من الخزانات المصممة خصيصاً لتناسب احتياجاتك وتضفي لمسة أناقة على منزلك في المملكة العربية السعودية.",url:"https://khobrakitchens.com/cabinets",type:"website"}};function i(){return(0,a.jsx)(r.default,{})}},5428:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>m,tree:()=>c});var a=s(5239),r=s(8088),l=s(8170),i=s.n(l),n=s(893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(t,o);let c={children:["",{children:["cabinets",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,5218)),"/var/www/html/src/app/cabinets/page.tsx"]}]},{metadata:{icon:[],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"/var/www/html/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]}.children,d=["/var/www/html/src/app/cabinets/page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/cabinets/page",pathname:"/cabinets",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},6569:(e,t,s)=>{Promise.resolve().then(s.bind(s,418))},6680:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var a=s(687),r=s(3210),l=s(8920),i=s(6001),n=s(2036),o=s(3372);s(2822),s(5866),s(4120);let c=({product:e,onClose:t,type:s})=>{(0,r.useEffect)(()=>{document.body.style.overflow="hidden";let e=e=>{"Escape"===e.key&&t()};return document.addEventListener("keydown",e),()=>{document.body.style.overflow="unset",document.removeEventListener("keydown",e)}},[t]);let c=[{name:"واتساب",icon:"\uD83D\uDCF1",url:`https://wa.me/966500000000?text=مرحباً، أريد الاستفسار عن ${e.title}`,color:"bg-green-500 hover:bg-green-600"},{name:"تويتر",icon:"\uD83D\uDC26",url:`https://twitter.com/intent/tweet?text=شاهد هذا ${"kitchen"===s?"المطبخ":"الخزانة"} الرائع من عجائب الخبراء: ${e.title}`,color:"bg-blue-500 hover:bg-blue-600"},{name:"فيسبوك",icon:"\uD83D\uDCD8",url:`https://www.facebook.com/sharer/sharer.php?u=${window.location.href}`,color:"bg-blue-600 hover:bg-blue-700"},{name:"إنستغرام",icon:"\uD83D\uDCF7",url:"#",color:"bg-pink-500 hover:bg-pink-600"}];return(0,a.jsx)(l.N,{children:(0,a.jsxs)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:[(0,a.jsx)(i.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/70 backdrop-blur-sm",onClick:t}),(0,a.jsx)("div",{className:"flex min-h-full items-center justify-center p-4",children:(0,a.jsxs)(i.P.div,{initial:{opacity:0,scale:.9,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.9,y:20},className:"relative bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden",onClick:e=>e.stopPropagation(),children:[(0,a.jsx)("button",{onClick:t,className:"absolute top-4 left-4 z-10 w-10 h-10 bg-black/50 hover:bg-black/70 text-white rounded-full flex items-center justify-center transition-colors",children:(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 h-full",children:[(0,a.jsxs)("div",{className:"relative h-64 lg:h-full",children:[e.images&&e.images.length>0?(0,a.jsx)(n.RC,{modules:[o.Vx,o.dK,o.Ij],navigation:!0,pagination:{clickable:!0},autoplay:{delay:4e3,disableOnInteraction:!1},className:"h-full",children:e.images.map(t=>(0,a.jsx)(n.qr,{children:(0,a.jsx)("img",{src:t.image_url,alt:t.alt_text||e.title,className:"w-full h-full object-cover"})},t.id))}):(0,a.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-gray-500 text-lg",children:"لا توجد صور"})}),e.category_name&&(0,a.jsx)("div",{className:`absolute top-4 right-4 px-3 py-1 rounded-full text-sm font-medium text-white ${"kitchen"===s?"bg-primary-500":"bg-secondary-500"}`,children:e.category_name})]}),(0,a.jsxs)("div",{className:"p-6 lg:p-8 flex flex-col justify-between overflow-y-auto",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl lg:text-3xl font-bold text-gray-900 mb-4",children:e.title}),e.description&&(0,a.jsx)("p",{className:"text-gray-600 leading-relaxed mb-6 text-lg",children:e.description}),(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:"المميزات:"}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-primary-500 rounded-full ml-3"}),"تصميم عصري وأنيق"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-primary-500 rounded-full ml-3"}),"خامات عالية الجودة"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-primary-500 rounded-full ml-3"}),"ضمان شامل"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-primary-500 rounded-full ml-3"}),"تركيب احترافي"]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[(0,a.jsxs)("a",{href:`https://wa.me/966500000000?text=مرحباً، أريد الاستفسار عن ${e.title}`,target:"_blank",rel:"noopener noreferrer",className:"bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-full font-bold text-center transition-colors flex items-center justify-center gap-2",children:[(0,a.jsx)("span",{children:"\uD83D\uDCF1"}),"واتساب"]}),(0,a.jsxs)("a",{href:"tel:+966500000000",className:`${"kitchen"===s?"bg-primary-500 hover:bg-primary-600":"bg-secondary-500 hover:bg-secondary-600"} text-white px-6 py-3 rounded-full font-bold text-center transition-colors flex items-center justify-center gap-2`,children:[(0,a.jsx)("span",{children:"\uD83D\uDCDE"}),"اتصل بنا"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-3",children:"شارك:"}),(0,a.jsx)("div",{className:"flex gap-3",children:c.map(e=>(0,a.jsx)("a",{href:e.url,target:"_blank",rel:"noopener noreferrer",className:`w-10 h-10 ${e.color} text-white rounded-full flex items-center justify-center transition-colors`,title:e.name,children:(0,a.jsx)("span",{className:"text-sm",children:e.icon})},e.name))})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500 border-t pt-4",children:[(0,a.jsx)("p",{children:"\uD83D\uDCA1 احصل على استشارة مجانية وعرض سعر مخصص"}),(0,a.jsx)("p",{children:"\uD83D\uDE9A توصيل وتركيب مجاني داخل الرياض"}),(0,a.jsx)("p",{children:"\uD83D\uDEE1️ ضمان شامل لمدة سنتين"})]})]})]})]})]})})]})})}},6841:(e,t,s)=>{Promise.resolve().then(s.bind(s,1544))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[447,317,866,814,426,434],()=>s(5428));module.exports=a})();